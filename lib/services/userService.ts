import { UserStats, UserConfiguration, DEFAULT_USER_CONFIG } from '@/types';
import { databases, appwriteConfig } from '@/lib/appwrite/config';
import { Query } from 'appwrite';

export class UserService {
  // Get user statistics from Appwrite database
  async getUserStats(userId: string): Promise<UserStats> {
    try {
      // Get user configuration from database
      const userConfig = await this.getUserConfiguration(userId);

      // Get current images count from images collection
      let currentImages = 0;
      try {
        const imagesResponse = await databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.imageCollectionId,
          [Query.equal('userId', userId)]
        );
        currentImages = imagesResponse.total;
      } catch (error) {
        console.warn('Could not fetch images count, using 0:', error);
      }

      // Calculate storage used (mock for now, would need to sum file sizes)
      const storageUsed = Math.floor(Math.random() * 500) + 100; // Mock data

      return {
        currentImages,
        totalImagesLimit: userConfig.totalImagesLimit,
        credits: userConfig.credits,
        storageUsed,
        storageLimit: 1000, // 1GB limit
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  // Update user configuration in Appwrite database
  async updateUserConfiguration(
    userId: string,
    config: Partial<UserConfiguration>
  ): Promise<UserConfiguration> {
    try {
      // Get current configuration
      const currentConfig = await this.getUserConfiguration(userId);
      const updatedConfig = { ...currentConfig, ...config };

      // Update the document in Appwrite
      await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId,
        config
      );

      console.log('Updated user configuration:', updatedConfig);
      return updatedConfig;
    } catch (error) {
      console.error('Error updating user configuration:', error);
      throw new Error('Failed to update user configuration');
    }
  }

  // Get user configuration from Appwrite database
  async getUserConfiguration(userId: string): Promise<UserConfiguration> {
    try {
      // Try to get existing user profile document
      const userDoc = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId
      );

      return {
        isAdmin: userDoc.isAdmin ?? DEFAULT_USER_CONFIG.isAdmin,
        totalImagesLimit: userDoc.totalImagesLimit ?? DEFAULT_USER_CONFIG.totalImagesLimit,
        credits: userDoc.credits ?? DEFAULT_USER_CONFIG.credits,
        theme: userDoc.theme ?? 'dark',
        notifications: userDoc.notifications ?? true,
      };
    } catch (error: any) {
      console.warn('Database access error for user configuration:', error.message);

      // If user document doesn't exist, try to create it
      if (error.code === 404) {
        try {
          return await this.createUserConfiguration(userId);
        } catch (createError: any) {
          console.warn('Could not create user configuration, using defaults:', createError.message);
          return this.getDefaultConfiguration();
        }
      }

      // If authorization error or other database error, return defaults
      if (error.code === 401 || error.message?.includes('not authorized')) {
        console.warn('User not authorized for database access, using default configuration');
        return this.getDefaultConfiguration();
      }

      console.error('Error fetching user configuration:', error);
      // Return defaults instead of throwing error
      return this.getDefaultConfiguration();
    }
  }

  // Get default configuration without database access
  private getDefaultConfiguration(): UserConfiguration {
    // Try to get from localStorage as fallback
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('userConfig');
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          return {
            isAdmin: parsed.isAdmin ?? DEFAULT_USER_CONFIG.isAdmin,
            totalImagesLimit: parsed.totalImagesLimit ?? DEFAULT_USER_CONFIG.totalImagesLimit,
            credits: parsed.credits ?? DEFAULT_USER_CONFIG.credits,
            theme: parsed.theme ?? 'dark',
            notifications: parsed.notifications ?? true,
          };
        } catch (error) {
          console.warn('Error parsing stored user config:', error);
        }
      }
    }

    return {
      isAdmin: DEFAULT_USER_CONFIG.isAdmin,
      totalImagesLimit: DEFAULT_USER_CONFIG.totalImagesLimit,
      credits: DEFAULT_USER_CONFIG.credits,
      theme: 'dark' as const,
      notifications: true,
    };
  }

  // Create user configuration with defaults (public method for auth service)
  async createUserConfiguration(userId: string): Promise<UserConfiguration> {
    try {
      const defaultConfig: UserConfiguration = {
        isAdmin: DEFAULT_USER_CONFIG.isAdmin,
        totalImagesLimit: DEFAULT_USER_CONFIG.totalImagesLimit,
        credits: DEFAULT_USER_CONFIG.credits,
        theme: 'dark' as const,
        notifications: true,
      };

      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId,
        defaultConfig
      );

      return defaultConfig;
    } catch (error) {
      console.error('Error creating user configuration:', error);
      throw new Error('Failed to create user configuration');
    }
  }

  // Ensure user exists in database (called during OAuth)
  async ensureUserExists(userId: string): Promise<UserConfiguration> {
    try {
      // Try to get existing configuration
      return await this.getUserConfiguration(userId);
    } catch (error) {
      // If user doesn't exist, create with defaults
      console.log(`Creating new user configuration for ${userId}`);
      return await this.createUserConfiguration(userId);
    }
  }

  // Update user credits in database
  async updateCredits(userId: string, amount: number): Promise<number> {
    try {
      const currentConfig = await this.getUserConfiguration(userId);
      const newCredits = currentConfig.credits + amount;

      // Update credits in database
      await this.updateUserConfiguration(userId, { credits: newCredits });

      console.log(`Updated credits for user ${userId}: ${newCredits}`);
      return newCredits;
    } catch (error) {
      console.error('Error updating credits:', error);
      throw new Error('Failed to update credits');
    }
  }

  // Check if user can upload more images
  async canUploadImage(userId: string): Promise<boolean> {
    try {
      const stats = await this.getUserStats(userId);
      return stats.currentImages < stats.totalImagesLimit;
    } catch (error) {
      console.error('Error checking upload permission:', error);
      return false;
    }
  }

  // Get remaining upload slots
  async getRemainingUploads(userId: string): Promise<number> {
    try {
      const stats = await this.getUserStats(userId);
      return Math.max(0, stats.totalImagesLimit - stats.currentImages);
    } catch (error) {
      console.error('Error calculating remaining uploads:', error);
      return 0;
    }
  }

  // Calculate storage usage percentage
  async getStorageUsagePercentage(userId: string): Promise<number> {
    try {
      const stats = await this.getUserStats(userId);
      return Math.min(100, (stats.storageUsed / stats.storageLimit) * 100);
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return 0;
    }
  }

  // Deduct credits for an action
  async deductCredits(userId: string, amount: number, reason: string): Promise<number> {
    try {
      const currentConfig = await this.getUserConfiguration(userId);

      if (currentConfig.credits < amount) {
        throw new Error('Insufficient credits');
      }

      const newCredits = currentConfig.credits - amount;

      // Update credits in database
      await this.updateUserConfiguration(userId, { credits: newCredits });

      console.log(`Deducted ${amount} credits for ${reason}. New balance: ${newCredits}`);

      return newCredits;
    } catch (error) {
      console.error('Error deducting credits:', error);
      throw error;
    }
  }

  // Add credits to user account
  async addCredits(userId: string, amount: number, reason: string): Promise<number> {
    try {
      const newCredits = await this.updateCredits(userId, amount);
      console.log(`Added ${amount} credits for ${reason}. New balance: ${newCredits}`);
      
      return newCredits;
    } catch (error) {
      console.error('Error adding credits:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const userService = new UserService();
