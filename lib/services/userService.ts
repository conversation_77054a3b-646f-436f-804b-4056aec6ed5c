import { UserStats, UserConfiguration } from '@/types';

export class UserService {
  // Get user statistics (mock data for now)
  async getUserStats(userId: string): Promise<UserStats> {
    try {
      // In a real implementation, this would fetch from Appwrite database
      // For now, return mock data
      return {
        currentImages: Math.floor(Math.random() * 50) + 10, // Random between 10-60
        totalImagesLimit: 100,
        credits: 150.25 + Math.random() * 100, // Random credits between 150-250
        storageUsed: Math.floor(Math.random() * 500) + 100, // Random MB between 100-600
        storageLimit: 1000, // 1GB limit
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  // Update user configuration
  async updateUserConfiguration(
    userId: string, 
    config: Partial<UserConfiguration>
  ): Promise<UserConfiguration> {
    try {
      // In a real implementation, this would update Appwrite database
      // For now, return the updated config
      const currentConfig = await this.getUserConfiguration(userId);
      const updatedConfig = { ...currentConfig, ...config };
      
      console.log('Updated user configuration:', updatedConfig);
      return updatedConfig;
    } catch (error) {
      console.error('Error updating user configuration:', error);
      throw new Error('Failed to update user configuration');
    }
  }

  // Get user configuration
  async getUserConfiguration(userId: string): Promise<UserConfiguration> {
    try {
      // In a real implementation, this would fetch from Appwrite database
      // For now, return default configuration
      return {
        isAdmin: true,
        totalImagesLimit: 100,
        credits: 200.00,
        theme: 'dark',
        notifications: true,
      };
    } catch (error) {
      console.error('Error fetching user configuration:', error);
      throw new Error('Failed to fetch user configuration');
    }
  }

  // Update user credits
  async updateCredits(userId: string, amount: number): Promise<number> {
    try {
      // In a real implementation, this would update Appwrite database
      const currentStats = await this.getUserStats(userId);
      const newCredits = currentStats.credits + amount;
      
      console.log(`Updated credits for user ${userId}: ${newCredits}`);
      return newCredits;
    } catch (error) {
      console.error('Error updating credits:', error);
      throw new Error('Failed to update credits');
    }
  }

  // Check if user can upload more images
  async canUploadImage(userId: string): Promise<boolean> {
    try {
      const stats = await this.getUserStats(userId);
      return stats.currentImages < stats.totalImagesLimit;
    } catch (error) {
      console.error('Error checking upload permission:', error);
      return false;
    }
  }

  // Get remaining upload slots
  async getRemainingUploads(userId: string): Promise<number> {
    try {
      const stats = await this.getUserStats(userId);
      return Math.max(0, stats.totalImagesLimit - stats.currentImages);
    } catch (error) {
      console.error('Error calculating remaining uploads:', error);
      return 0;
    }
  }

  // Calculate storage usage percentage
  async getStorageUsagePercentage(userId: string): Promise<number> {
    try {
      const stats = await this.getUserStats(userId);
      return Math.min(100, (stats.storageUsed / stats.storageLimit) * 100);
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return 0;
    }
  }

  // Deduct credits for an action
  async deductCredits(userId: string, amount: number, reason: string): Promise<number> {
    try {
      const currentStats = await this.getUserStats(userId);
      
      if (currentStats.credits < amount) {
        throw new Error('Insufficient credits');
      }
      
      const newCredits = currentStats.credits - amount;
      console.log(`Deducted ${amount} credits for ${reason}. New balance: ${newCredits}`);
      
      return newCredits;
    } catch (error) {
      console.error('Error deducting credits:', error);
      throw error;
    }
  }

  // Add credits to user account
  async addCredits(userId: string, amount: number, reason: string): Promise<number> {
    try {
      const newCredits = await this.updateCredits(userId, amount);
      console.log(`Added ${amount} credits for ${reason}. New balance: ${newCredits}`);
      
      return newCredits;
    } catch (error) {
      console.error('Error adding credits:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const userService = new UserService();
