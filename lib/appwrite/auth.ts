import { account } from './config';
import { OAuthProvider } from 'appwrite';
import { AuthUser } from '@/types';

export class AuthService {
  // Initiate Discord OAuth login
  async loginWithDiscord(successURL: string, failureURL: string): Promise<void> {
    try {
      // This will redirect the user to Discord for authentication
      account.createOAuth2Session(OAuthProvider.Discord, successURL, failureURL);
    } catch (error: any) {
      console.error('Discord OAuth error:', error);
      throw new Error(error.message || 'Discord authentication failed');
    }
  }

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const user = await account.get();
      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await account.deleteSession('current');
    } catch (error: any) {
      console.error('Logout error:', error);
      throw new Error(error.message || 'Logout failed');
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      await account.get();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get user sessions
  async getSessions() {
    try {
      return await account.listSessions();
    } catch (error: any) {
      console.error('Get sessions error:', error);
      throw new Error(error.message || 'Failed to get sessions');
    }
  }

  // Delete all sessions (logout from all devices)
  async logoutFromAllDevices(): Promise<void> {
    try {
      await account.deleteSessions();
    } catch (error: any) {
      console.error('Logout from all devices error:', error);
      throw new Error(error.message || 'Failed to logout from all devices');
    }
  }

  // Update user name (for OAuth users)
  async updateName(name: string): Promise<AuthUser> {
    try {
      const user = await account.updateName(name);
      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
      };
    } catch (error: any) {
      console.error('Update name error:', error);
      throw new Error(error.message || 'Failed to update name');
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
