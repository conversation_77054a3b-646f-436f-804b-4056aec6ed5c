import { account, ID } from './config';
import { LoginCredentials, RegisterCredentials, AuthUser } from '@/types';

export class AuthService {
  // Register new user
  async register({ name, email, password }: RegisterCredentials): Promise<AuthUser> {
    try {
      const newUser = await account.create(ID.unique(), email, password, name);
      
      // Automatically log in the user after registration
      await this.login({ email, password });
      
      return {
        $id: newUser.$id,
        name: newUser.name,
        email: newUser.email,
      };
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(error.message || 'Registration failed');
    }
  }

  // Login user
  async login({ email, password }: LoginCredentials): Promise<AuthUser> {
    try {
      await account.createEmailPasswordSession(email, password);
      const user = await account.get();
      
      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
      };
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.message || 'Login failed');
    }
  }

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const user = await account.get();
      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await account.deleteSession('current');
    } catch (error: any) {
      console.error('Logout error:', error);
      throw new Error(error.message || 'Logout failed');
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      await account.get();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get user sessions
  async getSessions() {
    try {
      return await account.listSessions();
    } catch (error: any) {
      console.error('Get sessions error:', error);
      throw new Error(error.message || 'Failed to get sessions');
    }
  }

  // Delete all sessions (logout from all devices)
  async logoutFromAllDevices(): Promise<void> {
    try {
      await account.deleteSessions();
    } catch (error: any) {
      console.error('Logout from all devices error:', error);
      throw new Error(error.message || 'Failed to logout from all devices');
    }
  }

  // Update user name
  async updateName(name: string): Promise<AuthUser> {
    try {
      const user = await account.updateName(name);
      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
      };
    } catch (error: any) {
      console.error('Update name error:', error);
      throw new Error(error.message || 'Failed to update name');
    }
  }

  // Update user email
  async updateEmail(email: string, password: string): Promise<AuthUser> {
    try {
      const user = await account.updateEmail(email, password);
      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
      };
    } catch (error: any) {
      console.error('Update email error:', error);
      throw new Error(error.message || 'Failed to update email');
    }
  }

  // Update user password
  async updatePassword(newPassword: string, oldPassword: string): Promise<void> {
    try {
      await account.updatePassword(newPassword, oldPassword);
    } catch (error: any) {
      console.error('Update password error:', error);
      throw new Error(error.message || 'Failed to update password');
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
